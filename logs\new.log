Using device: cuda

============================================================
Quick Test: DKDM Algorithm
============================================================
Starting quick test with dkdm...
2025-08-05 07:47:35 - d3afd_train - INFO - Starting D³AFD experiment with dkdm algorithm
2025-08-05 07:47:35 - d3afd_train - INFO - Output directory: outputs/quick_test_dkdm/dkdm_20250805_074735
2025-08-05 07:47:35 - d3afd_train - INFO - Loading Amazon Reviews dataset...
2025-08-05 07:47:35 - d3afd_train - INFO - Setting up 2 clients for domains: ['Electronics', 'Books']
Client client_0_Electronics: No local data provided or empty data (len: 0)
2025-08-05 07:47:36 - d3afd_train - INFO - Created client client_0_Electronics with 0 samples
Client client_1_Books: No local data provided or empty data (len: 0)
2025-08-05 07:47:36 - d3afd_train - INFO - Created client client_1_Books with 0 samples
2025-08-05 07:47:36 - d3afd_train - INFO - Initializing federated server with dkdm algorithm...
You are using the default legacy behaviour of the <class 'transformers.models.t5.tokenization_t5.T5Tokenizer'>. This is expected, and simply means that the `legacy` (previous) behavior will be used so nothing changes for you. If you want to use the new behaviour, set `legacy=False`. This should only be set if you understand what it means, and thoroughly read the reason why this was added as explained in https://github.com/huggingface/transformers/pull/24565
2025-08-05 07:47:41 - src.algorithms.dkdm_distillation - INFO - DKDM distillation trainer initialized
2025-08-05 07:47:41 - src.utils.metrics - INFO - Metrics tracker initialized for dkdm algorithm
2025-08-05 07:47:41 - d3afd_train - INFO - Starting federated training...
2025-08-05 07:47:41 - d3afd_train - INFO - 
==================================================
2025-08-05 07:47:41 - d3afd_train - INFO - Round 1/3
2025-08-05 07:47:41 - d3afd_train - INFO - Algorithm: dkdm
2025-08-05 07:47:41 - d3afd_train - INFO - ==================================================
2025-08-05 07:47:41 - d3afd_train - INFO - Phase 1: Local training on clients...
2025-08-05 07:47:41 - d3afd_train - INFO - Training client client_0_Electronics...
Client client_0_Electronics: No model or data for training
2025-08-05 07:47:42 - d3afd_train - INFO - Training client client_1_Books...
Client client_1_Books: No model or data for training
2025-08-05 07:47:43 - d3afd_train - INFO - Phase 2: Global distillation...
客户端 client_0_Electronics 没有teacher_model，跳过域描述符生成
客户端 client_1_Books 没有teacher_model，跳过域描述符生成
2025-08-05 07:48:45 - src.algorithms.dkdm_distillation - INFO - Starting DKDM distillation training...
2025-08-05 07:48:45 - src.algorithms.dkdm_distillation - WARNING - Cache file cache/dkdm_cache_8248708090803975561.pkl not found
2025-08-05 07:48:45 - src.algorithms.dkdm_distillation - INFO - Generating new DKDM cache...
2025-08-05 07:48:45 - src.algorithms.dkdm_distillation - INFO - Generating DKDM pseudo data cache...
2025-08-05 07:48:45 - src.algorithms.dkdm_distillation - INFO - DKDM cache saved to cache/dkdm_cache_8248708090803975561.pkl with 160 samples
2025-08-05 07:48:45 - src.algorithms.dkdm_distillation - INFO - DKDM cache loaded from cache/dkdm_cache_8248708090803975561.pkl
2025-08-05 07:48:47 - src.algorithms.dkdm_distillation - INFO - DKDM Epoch 1/2, Loss: 0.0582
2025-08-05 07:48:50 - src.algorithms.dkdm_distillation - INFO - DKDM Epoch 2/2, Loss: 0.0096
2025-08-05 07:48:50 - src.utils.metrics - INFO - Round 0 metrics logged: {'round': 0, 'algorithm': 'dkdm', 'dkdm_loss': 0.03392673243797617, 'epochs': 2, 'total_batches': 2}
2025-08-05 07:48:50 - d3afd_train - INFO - Phase 3: Evaluation...
2025-08-05 07:48:50 - d3afd_train - ERROR - Error in round 1: list index out of range
2025-08-05 07:48:50 - d3afd_train - INFO - 
==================================================
2025-08-05 07:48:50 - d3afd_train - INFO - Round 2/3
2025-08-05 07:48:50 - d3afd_train - INFO - Algorithm: dkdm
2025-08-05 07:48:50 - d3afd_train - INFO - ==================================================
2025-08-05 07:48:50 - d3afd_train - INFO - Phase 1: Local training on clients...
2025-08-05 07:48:50 - d3afd_train - INFO - Training client client_0_Electronics...
Client client_0_Electronics: No model or data for training
2025-08-05 07:48:50 - d3afd_train - INFO - Training client client_1_Books...
Client client_1_Books: No model or data for training
2025-08-05 07:48:50 - d3afd_train - INFO - Phase 2: Global distillation...
客户端 client_0_Electronics 没有teacher_model，跳过域描述符生成
客户端 client_1_Books 没有teacher_model，跳过域描述符生成
2025-08-05 07:49:54 - src.algorithms.dkdm_distillation - INFO - Starting DKDM distillation training...
2025-08-05 07:49:54 - src.algorithms.dkdm_distillation - INFO - DKDM cache loaded from cache/dkdm_cache_8248708090803975561.pkl
2025-08-05 07:49:57 - src.algorithms.dkdm_distillation - INFO - DKDM Epoch 1/2, Loss: 0.0197
2025-08-05 07:49:59 - src.algorithms.dkdm_distillation - INFO - DKDM Epoch 2/2, Loss: 0.0060
2025-08-05 07:49:59 - src.utils.metrics - INFO - Round 1 metrics logged: {'round': 1, 'algorithm': 'dkdm', 'dkdm_loss': 0.012838789327361155, 'epochs': 2, 'total_batches': 2}
2025-08-05 07:49:59 - d3afd_train - INFO - Phase 3: Evaluation...
2025-08-05 07:49:59 - d3afd_train - ERROR - Error in round 2: list index out of range
2025-08-05 07:49:59 - d3afd_train - INFO - 
==================================================
2025-08-05 07:49:59 - d3afd_train - INFO - Round 3/3
2025-08-05 07:49:59 - d3afd_train - INFO - Algorithm: dkdm
2025-08-05 07:49:59 - d3afd_train - INFO - ==================================================
2025-08-05 07:49:59 - d3afd_train - INFO - Phase 1: Local training on clients...
2025-08-05 07:49:59 - d3afd_train - INFO - Training client client_0_Electronics...
Client client_0_Electronics: No model or data for training
2025-08-05 07:49:59 - d3afd_train - INFO - Training client client_1_Books...
Client client_1_Books: No model or data for training
2025-08-05 07:49:59 - d3afd_train - INFO - Phase 2: Global distillation...
客户端 client_0_Electronics 没有teacher_model，跳过域描述符生成
客户端 client_1_Books 没有teacher_model，跳过域描述符生成
2025-08-05 07:51:02 - src.algorithms.dkdm_distillation - INFO - Starting DKDM distillation training...
2025-08-05 07:51:02 - src.algorithms.dkdm_distillation - INFO - DKDM cache loaded from cache/dkdm_cache_8248708090803975561.pkl
2025-08-05 07:51:05 - src.algorithms.dkdm_distillation - INFO - DKDM Epoch 1/2, Loss: 0.0158
2025-08-05 07:51:07 - src.algorithms.dkdm_distillation - INFO - DKDM Epoch 2/2, Loss: 0.0079
2025-08-05 07:51:07 - src.utils.metrics - INFO - Round 2 metrics logged: {'round': 2, 'algorithm': 'dkdm', 'dkdm_loss': 0.011816234626166988, 'epochs': 2, 'total_batches': 2}
2025-08-05 07:51:07 - d3afd_train - INFO - Phase 3: Evaluation...
2025-08-05 07:51:07 - d3afd_train - ERROR - Error in round 3: list index out of range
2025-08-05 07:51:07 - d3afd_train - INFO - 
Final evaluation...
Error in dkdm quick test: list index out of range

DKDM quick test failed!