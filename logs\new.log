2025-08-06 01:26:54,671 - quick_test_main - INFO - Quick test started with device: cuda
2025-08-06 01:26:54,671 - quick_test_main - INFO - Main log file: logs/quick_test_diffdfkd_20250806_012654.log
2025-08-06 01:26:54,671 - quick_test_main - INFO - Testing single algorithm: diffdfkd
2025-08-06 01:27:03,567 - quick_test_diffdfkd - INFO - Logging to file: logs/quick_test_diffdfkd_20250806_012703.log
2025-08-06 01:27:03,567 - d3afd_train - INFO - Starting D³AFD experiment with diffdfkd algorithm
2025-08-06 01:27:03,568 - d3afd_train - INFO - Output directory: outputs/quick_test_diffdfkd/diffdfkd_20250806_012703
2025-08-06 01:27:03,568 - d3afd_train - INFO - Loading Amazon Reviews dataset...
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - Loading Amazon Reviews dataset...
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - Config type: <class 'src.utils.config.Config'>
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - Config.data available: True
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - Config.data.domains: ['Electronics', 'Books']
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - Using domains: ['Electronics', 'Books']
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - Samples per domain: 100
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - Loaded 200 samples across 2 domains
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - AmazonReviewDataset initialized with 200 samples
2025-08-06 01:27:04,356 - d3afd_train - INFO - Setting up 2 clients for domains: ['Electronics', 'Books']
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - Getting domain data for 'Electronics'
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - Total samples in dataset: 200
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - Available domains: ['Books', 'Electronics']
2025-08-06 01:27:04,356 - src.data.amazon_dataset - INFO - Retrieved 100 samples for domain Electronics
2025-08-06 01:27:05,111 - src.federated.client - INFO - Client client_0_Electronics: Creating dataloader from 100 samples
2025-08-06 01:27:05,111 - src.data.amazon_dataset - INFO - AmazonReviewDataset initialized with 100 samples
2025-08-06 01:27:12,830 - src.federated.client - INFO - Client client_0_Electronics initialized with domain: Electronics
2025-08-06 01:27:12,831 - d3afd_train - INFO - Created client client_0_Electronics with 100 samples
2025-08-06 01:27:12,831 - src.data.amazon_dataset - INFO - Getting domain data for 'Books'
2025-08-06 01:27:12,831 - src.data.amazon_dataset - INFO - Total samples in dataset: 200
2025-08-06 01:27:12,831 - src.data.amazon_dataset - INFO - Available domains: ['Books', 'Electronics']
2025-08-06 01:27:12,831 - src.data.amazon_dataset - INFO - Retrieved 100 samples for domain Books
2025-08-06 01:27:13,590 - src.federated.client - INFO - Client client_1_Books: Creating dataloader from 100 samples
2025-08-06 01:27:13,590 - src.data.amazon_dataset - INFO - AmazonReviewDataset initialized with 100 samples
2025-08-06 01:27:14,205 - src.federated.client - INFO - Client client_1_Books initialized with domain: Books
2025-08-06 01:27:14,205 - d3afd_train - INFO - Created client client_1_Books with 100 samples
2025-08-06 01:27:14,205 - d3afd_train - INFO - Initializing federated server with diffdfkd algorithm...
2025-08-06 01:27:15,564 - src.models.text_generator - INFO - Loading pretrained T5 model: t5-small
2025-08-06 01:27:18,693 - src.models.text_generator - INFO - T5生成器支持动态域描述符生成
2025-08-06 01:27:20,885 - src.algorithms.diffdfkd_distillation - INFO - DiffDFKD distillation trainer initialized with T5 text generation
2025-08-06 01:27:20,886 - src.federated.server - INFO - Initialized D³AFD server with diffdfkd distillation algorithm
2025-08-06 01:27:20,886 - src.federated.server - INFO - Federated server initialized
2025-08-06 01:27:20,886 - src.utils.metrics - INFO - Metrics tracker initialized for diffdfkd algorithm
2025-08-06 01:27:20,886 - d3afd_train - INFO - Starting federated training...
2025-08-06 01:27:20,886 - d3afd_train - INFO - 
==================================================
2025-08-06 01:27:20,886 - d3afd_train - INFO - Round 1/3
2025-08-06 01:27:20,887 - d3afd_train - INFO - Algorithm: diffdfkd
2025-08-06 01:27:20,887 - d3afd_train - INFO - ==================================================
2025-08-06 01:27:20,887 - d3afd_train - INFO - Phase 1: Local training on clients...
2025-08-06 01:27:20,887 - d3afd_train - INFO - Training client client_0_Electronics...
2025-08-06 01:27:20,887 - src.federated.client - INFO - Client client_0_Electronics: Training local teacher for 2 epochs
2025-08-06 01:27:25,726 - src.federated.client - INFO - Client client_0_Electronics - Epoch 1/2: Loss: 0.6237, Accuracy: 0.9700
2025-08-06 01:27:28,042 - src.federated.client - INFO - Client client_0_Electronics - Epoch 2/2: Loss: 0.2965, Accuracy: 1.0000
2025-08-06 01:27:28,265 - src.federated.client - INFO - Client client_0_Electronics: Local teacher training completed. Final accuracy: 0.9850
2025-08-06 01:27:28,266 - d3afd_train - INFO - Training client client_1_Books...
2025-08-06 01:27:28,267 - src.federated.client - INFO - Client client_1_Books: Training local teacher for 2 epochs
2025-08-06 01:27:30,378 - src.federated.client - INFO - Client client_1_Books - Epoch 1/2: Loss: 0.6987, Accuracy: 0.8500
2025-08-06 01:27:32,727 - src.federated.client - INFO - Client client_1_Books - Epoch 2/2: Loss: 0.2976, Accuracy: 1.0000
2025-08-06 01:27:32,951 - src.federated.client - INFO - Client client_1_Books: Local teacher training completed. Final accuracy: 0.9250
2025-08-06 01:27:32,952 - d3afd_train - INFO - Phase 2: Global distillation...
2025-08-06 01:27:32,953 - src.utils.memory_manager - INFO - GPU Memory before_distillation_round: Allocated: 1.72GB, Reserved: 2.42GB, Free: 12.16GB
2025-08-06 01:27:32,953 - src.federated.server - INFO - Starting distillation round 0
2025-08-06 01:27:32,953 - src.utils.memory_manager - INFO - GPU Memory start_distillation_round_0: Allocated: 1.72GB, Reserved: 2.42GB, Free: 12.16GB
2025-08-06 01:27:32,953 - src.federated.server - INFO - 🔄 生成动态域描述符...
2025-08-06 01:27:32,953 - src.federated.server - INFO - 第 0 轮: 生成动态域描述符
2025-08-06 01:27:32,954 - src.utils.memory_manager - INFO - GPU Memory start_dynamic_descriptors_round_0: Allocated: 1.72GB, Reserved: 2.42GB, Free: 12.16GB
2025-08-06 01:27:32,954 - src.federated.server - INFO - 处理客户端 client_0_Electronics 的域描述符...
2025-08-06 01:27:33,144 - src.models.dynamic_domain_descriptor - INFO - 动态域描述符生成器初始化完成
2025-08-06 01:27:33,145 - src.models.dynamic_domain_descriptor - INFO - 为客户端 client_0_Electronics 生成第 0 轮域描述符
2025-08-06 01:27:33,145 - src.models.dynamic_domain_descriptor - INFO - 提取域特征，最大样本数: 200
2025-08-06 01:27:33,832 - src.models.dynamic_domain_descriptor - INFO - 提取了 100 个样本的特征
2025-08-06 01:27:33,840 - src.models.dynamic_domain_descriptor - INFO - 客户端 client_0_Electronics 域描述符生成完成:
2025-08-06 01:27:33,840 - src.models.dynamic_domain_descriptor - INFO -   域中心范数: 0.9408
2025-08-06 01:27:33,840 - src.models.dynamic_domain_descriptor - INFO -   域方差均值: 0.0097
2025-08-06 01:27:33,841 - src.models.dynamic_domain_descriptor - INFO -   域密度: 1.2777
2025-08-06 01:27:33,841 - src.federated.server - INFO - 客户端 client_0_Electronics 域描述符生成完成
2025-08-06 01:27:33,841 - src.federated.server - INFO -   演化趋势: unknown
2025-08-06 01:27:33,841 - src.federated.server - INFO -   稳定性: 0.0000
2025-08-06 01:27:34,444 - src.federated.server - INFO - 处理客户端 client_1_Books 的域描述符...
2025-08-06 01:27:34,623 - src.models.dynamic_domain_descriptor - INFO - 动态域描述符生成器初始化完成
2025-08-06 01:27:34,623 - src.models.dynamic_domain_descriptor - INFO - 为客户端 client_1_Books 生成第 0 轮域描述符
2025-08-06 01:27:34,623 - src.models.dynamic_domain_descriptor - INFO - 提取域特征，最大样本数: 200
2025-08-06 01:27:35,320 - src.models.dynamic_domain_descriptor - INFO - 提取了 100 个样本的特征
2025-08-06 01:27:35,321 - src.models.dynamic_domain_descriptor - INFO - 客户端 client_1_Books 域描述符生成完成:
2025-08-06 01:27:35,322 - src.models.dynamic_domain_descriptor - INFO -   域中心范数: 1.0476
2025-08-06 01:27:35,322 - src.models.dynamic_domain_descriptor - INFO -   域方差均值: 0.0110
2025-08-06 01:27:35,322 - src.models.dynamic_domain_descriptor - INFO -   域密度: 1.2002
2025-08-06 01:27:35,322 - src.federated.server - INFO - 客户端 client_1_Books 域描述符生成完成
2025-08-06 01:27:35,322 - src.federated.server - INFO -   演化趋势: unknown
2025-08-06 01:27:35,322 - src.federated.server - INFO -   稳定性: 0.0000
2025-08-06 01:27:35,904 - src.models.text_generator - INFO - 更新域描述符，客户端数量: 2
2025-08-06 01:27:35,904 - src.models.text_generator - INFO - 客户端 client_0_Electronics 第 0 轮域描述符已更新
2025-08-06 01:27:35,904 - src.models.text_generator - INFO - 客户端 client_1_Books 第 0 轮域描述符已更新
2025-08-06 01:27:36,437 - src.utils.memory_manager - INFO - GPU Memory end_dynamic_descriptors_round_0: Allocated: 1.72GB, Reserved: 2.04GB, Free: 12.54GB
2025-08-06 01:27:36,437 - src.federated.server - INFO - 动态域描述符生成完成，客户端数量: 2
2025-08-06 01:27:36,437 - src.federated.server - INFO - DiffDFKD will generate synthetic data using T5
2025-08-06 01:27:36,437 - src.federated.server - INFO - Training global student model with diffdfkd distillation...
2025-08-06 01:27:36,438 - src.utils.memory_manager - INFO - GPU Memory before_train_student_model: Allocated: 1.72GB, Reserved: 2.04GB, Free: 12.54GB
2025-08-06 01:27:36,438 - src.algorithms.diffdfkd_distillation - INFO - Starting DiffDFKD distillation training with T5 text generation...
2025-08-06 01:27:36,438 - src.algorithms.diffdfkd_distillation - INFO - Registered 0 feature hooks for BatchNorm regularization
2025-08-06 01:27:50,782 - src.algorithms.diffdfkd_distillation - INFO - Generated 100 synthetic reviews for domain Electronics
2025-08-06 01:28:05,185 - src.algorithms.diffdfkd_distillation - INFO - Generated 100 synthetic reviews for domain Books
2025-08-06 01:28:05,349 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:05,505 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:05,532 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:05,656 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:05,764 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:05,874 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:05,938 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:06,052 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:06,137 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:06,252 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:06,373 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:06,451 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:06,571 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: SentimentClassifier.forward() missing 1 required positional argument: 'attention_mask'
2025-08-06 01:28:06,720 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,749 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,750 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,752 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,756 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,758 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,761 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,763 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,766 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,768 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,769 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,968 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:06,998 - src.algorithms.diffdfkd_distillation - INFO - DiffDFKD Epoch 1/2, Total Loss: 0.0000, OH: 0.0000, BN: 0.0000, ADV: 0.0000
2025-08-06 01:28:07,049 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,092 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,093 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,099 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,100 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,105 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,106 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,113 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,114 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,119 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,120 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,127 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,127 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,133 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,134 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,141 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,141 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,147 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,149 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,155 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,155 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,161 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,162 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,165 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,166 - src.algorithms.diffdfkd_distillation - ERROR - Error in DiffDFKD training batch: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,192 - src.algorithms.diffdfkd_distillation - INFO - DiffDFKD Epoch 2/2, Total Loss: 0.0000, OH: 0.0000, BN: 0.0000, ADV: 0.0000
2025-08-06 01:28:07,555 - d3afd_train - ERROR - Error in round 1: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,555 - d3afd_train - INFO - 
==================================================
2025-08-06 01:28:07,555 - d3afd_train - INFO - Round 2/3
2025-08-06 01:28:07,555 - d3afd_train - INFO - Algorithm: diffdfkd
2025-08-06 01:28:07,555 - d3afd_train - INFO - ==================================================
2025-08-06 01:28:07,555 - d3afd_train - INFO - Phase 1: Local training on clients...
2025-08-06 01:28:07,555 - d3afd_train - INFO - Training client client_0_Electronics...
2025-08-06 01:28:07,555 - src.federated.client - INFO - Client client_0_Electronics: Training local teacher for 2 epochs
2025-08-06 01:28:07,640 - d3afd_train - ERROR - Error in round 2: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,640 - d3afd_train - INFO - 
==================================================
2025-08-06 01:28:07,640 - d3afd_train - INFO - Round 3/3
2025-08-06 01:28:07,640 - d3afd_train - INFO - Algorithm: diffdfkd
2025-08-06 01:28:07,640 - d3afd_train - INFO - ==================================================
2025-08-06 01:28:07,640 - d3afd_train - INFO - Phase 1: Local training on clients...
2025-08-06 01:28:07,641 - d3afd_train - INFO - Training client client_0_Electronics...
2025-08-06 01:28:07,641 - src.federated.client - INFO - Client client_0_Electronics: Training local teacher for 2 epochs
2025-08-06 01:28:07,725 - d3afd_train - ERROR - Error in round 3: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,725 - d3afd_train - INFO - 
Final evaluation...
2025-08-06 01:28:07,726 - src.data.amazon_dataset - INFO - Retrieved 40 test samples
2025-08-06 01:28:07,726 - d3afd_train - INFO - Final evaluation with 40 test samples
2025-08-06 01:28:07,726 - src.federated.server - INFO - Evaluating global model on 40 test samples
2025-08-06 01:28:07,726 - src.data.amazon_dataset - INFO - AmazonReviewDataset initialized with 40 samples
2025-08-06 01:28:07,806 - d3afd_train - ERROR - Final evaluation failed: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-08-06 01:28:07,807 - d3afd_train - ERROR - Traceback: Traceback (most recent call last):
  File "/workspace/amazon-re-fd/train_d3afd_multi_algorithm.py", line 163, in run_experiment
    final_metrics = server.evaluate_global_model(test_data)
  File "/workspace/amazon-re-fd/src/federated/server.py", line 667, in evaluate_global_model
    input_ids = batch['input_ids'].to(self.device)
RuntimeError: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


2025-08-06 01:28:07,807 - src.utils.metrics - INFO - Final metrics logged: {'accuracy': 0.0, 'f1_macro': 0.0}
2025-08-06 01:28:07,873 - quick_test_main - ERROR - DIFFDFKD - FAILED
2025-08-06 01:28:07,874 - quick_test_main - INFO - Quick test session completed
